# Design-First Agent Implementation

## Overview

This implementation converts the first message flow from a single large generation to an agent-driven workflow that:
- **Detects screenshots** and adapts workflow accordingly
- **Always starts with queryCodebase** to understand default files
- **Prioritizes HomeScreen** for immediate user feedback
- **Focuses on 1-2 key features** rather than building everything
- **Maintains proper architecture** while delivering immediate value

## Key Changes Made

### 1. Route Configuration (`src/app/api/chat/route.ts`)

**Lines 489-496**: Enable agent mode for first messages
```typescript
// CHANGED: Enable agent mode for first messages to support design-first workflow
if (isFirstMessage) {
    agentModeEnabled = true;  // Enable agent mode for design-first approach
}
```

**Lines 1067-1089**: Configure tools for first message workflow with screenshot detection
```typescript
if (isFirstMessage) {
    // CHANGED: First message design-first workflow with screenshot detection
    enabledTools.push('queryCodebase');   // NEW: Always check default files first
    if (!hasScreenshots) {
        enabledTools.push('generateDesign');  // Only if no screenshots provided
    }
    enabledTools.push('editFile');        // File creation for architecture
}
```

**Lines 1382-1390**: Add generateDesign tool with first message optimization
```typescript
generateDesign: generateDesign({
    dataStream,
    chatId: id,
    projectId,
    existingScreens: [], // Always empty for first message
    maxParallelScreens: 2, // Limit to 2 screens for first message
    maxTotalScreens: isFirstMessage ? 2 : 6 // Limit total screens for cost control
}),
```

### 2. Message Handler (`src/lib/chat/handlers/message.handler.ts`)

**Lines 78-81**: Use new design-first agent prompt
```typescript
} else if (options?.isFirstUserMessage) {
    // CHANGED: Use new design-first agent prompt for first messages
    system = DESIGN_FIRST_AGENT_PROMPT;
} else {
```

### 3. New Agent Prompt (`src/lib/ai/prompts/design-first-agent-prompt.ts`)

Created comprehensive agent prompt that:
- Detects screenshots and adapts workflow accordingly
- Guides through 4-5 tool calls (depending on screenshots)
- Establishes proper architectural patterns
- Enforces file size limits
- Teaches good development practices
- Focuses on non-technical user experience
- Ensures exact recreation when screenshots are provided

### 4. Generate Design Tool Optimization (`src/lib/chat/tools/generate-design.tool.ts`)

**Enhanced for first message workflow:**
- Added `isFirstMessage` parameter to schema
- Enforces 2-screen limit for first messages
- Optimized temperature for creativity vs consistency
- Added first message specific instructions

## Screenshot Detection Logic

### Critical Problem Solved
**Issue**: When users upload screenshots and ask to recreate that exact design, the `generateDesign` tool cannot see the screenshots and would create a completely different design.

**Solution**: The agent now detects screenshots and adapts its workflow:

### Detection Logic
```typescript
// In agent's thinking process:
if (user_uploaded_screenshots && asks_to_recreate_design) {
    // SKIP generateDesign tool entirely
    // Extract colors/design from screenshots
    // Use 4 tool calls total
} else {
    // Use generateDesign tool for original design
    // Use 5 tool calls total
}
```

### Benefits
- ✅ **Exact Recreation**: When screenshots provided, recreates EXACTLY what user wants
- ✅ **Cost Savings**: Saves 1 tool call when screenshots are provided
- ✅ **No Design Conflicts**: Avoids generating different design than requested
- ✅ **Smart Adaptation**: Agent automatically chooses the right workflow

## Expected Workflow

### Agent Workflow (No Tool Call Limits)

**Universal First Step:**
1. **queryCodebase**: Understand default files and project structure

**If User Uploads Screenshots:**
2. **editFile**: `constants/colors.ts` (extract from screenshots)
3. **editFile**: `mocks/[domain].ts` (realistic mock data)
4. **editFile**: `screens/HomeScreen.tsx` (recreate exact design) - **PRIORITY**
5. **editFile**: `store/[domain]Store.ts` (Zustand store)
6. **editFile**: `App.tsx` (navigation and theme)

**If No Screenshots:**
2. **generateDesign**: Create 2 stunning reference screens
3. **editFile**: `constants/colors.ts` (extract from design)
4. **editFile**: `mocks/[domain].ts` (realistic mock data)
5. **editFile**: `screens/HomeScreen.tsx` (follow design reference) - **PRIORITY**
6. **editFile**: `store/[domain]Store.ts` (Zustand store)
7. **editFile**: `App.tsx` (navigation and theme)

**Key Principles:**
- **No tool call limits** - focus on quality over quantity
- **HomeScreen first** - immediate user feedback
- **1-2 key features maximum** - don't build everything
- **Proper architecture** - clean patterns from day 1

### File Architecture Established

```
constants/
  colors.ts          # Clean color system from design
store/
  appStore.ts        # Proper Zustand implementation
screens/
  HomeScreen.tsx     # Well-architected component
App.tsx              # Professional navigation setup
```

## Benefits

### For Non-Technical Users
- ✅ **Instant Visual Impact**: Beautiful design reference in 30 seconds
- ✅ **Working Functionality**: HomeScreen operational in 2 minutes
- ✅ **Professional Quality**: Production-ready code from start
- ✅ **Immediate Value**: Can show and use the app right away

### For Development Quality
- ✅ **Proper Architecture**: Clean patterns established from day 1
- ✅ **Scalable Foundation**: Easy to extend without refactoring
- ✅ **File Size Discipline**: Enforced limits prevent mega-files
- ✅ **Teaching Patterns**: Agent learns good practices for future

### For Cost Control
- ✅ **Smart Design Logic**: Skip generateDesign when screenshots provided
- ✅ **Focused Scope**: 1-2 key features maximum, not everything
- ✅ **Efficient Queries**: queryCodebase first to understand existing structure
- ✅ **Strategic Implementation**: Each file serves multiple purposes
- ✅ **Quality Focus**: Better to build less perfectly than more poorly

## Key Architectural Principles

### File Size Guidelines (Suggested, Not Strict)
- `constants/colors.ts`: 50-75 lines
- Store files: 100-150 lines
- Screen files: 150-200 lines (can be larger if needed)
- `App.tsx`: 75-100 lines

### Code Quality Standards
- Single responsibility principle
- Proper TypeScript typing
- Clean imports/exports
- Modular, reusable patterns
- Comments for future refactoring

### State Management Pattern
```typescript
// Proper Zustand store
export const useAppStore = create<AppState>((set, get) => ({
  items: MOCK_DATA,
  addItem: (item) => set((state) => ({
    items: [...state.items, item]
  })),
  // Clean, focused operations
}));
```

## Key Improvements Made

### 1. **Real Screenshot Detection**
- **Problem**: Agent couldn't detect screenshots, would use generateDesign anyway
- **Solution**: Added `hasScreenshots` detection in route handler
- **Result**: Agent automatically skips generateDesign when screenshots provided

### 2. **queryCodebase First**
- **Problem**: Agent forgot about default files we provide
- **Solution**: Always start with queryCodebase to understand existing structure
- **Result**: Agent builds on existing foundation instead of starting from scratch

### 3. **HomeScreen Priority**
- **Problem**: Users waited too long to see visual progress
- **Solution**: Build HomeScreen.tsx first for immediate preview
- **Result**: Users see working app within 2-3 minutes

### 4. **Focused Scope**
- **Problem**: Agent tried to build everything, resulting in poor quality
- **Solution**: Focus on 1-2 key features maximum
- **Result**: Higher quality implementation of core functionality

### 5. **No Tool Call Limits**
- **Problem**: Artificial constraints led to rushed implementations
- **Solution**: Remove limits but emphasize quality over quantity
- **Result**: Proper architecture without artificial constraints

## Expected Timeline

```
0-30 seconds:   queryCodebase (understand defaults)
30-60 seconds:  Design generation or screenshot analysis
60-120 seconds: HomeScreen creation (user sees working app)
120-180 seconds: Supporting files (store, navigation)
180-240 seconds: Polish and connections

Total: 3-4 minute delivery of stunning, functional app with proper architecture
```

## Future Development

When users request additional features, the agent will:
1. **Follow Established Patterns**: Use the architecture from first message
2. **Maintain File Sizes**: Keep components under 200 lines
3. **Extend Cleanly**: Add to existing stores and navigation
4. **Preserve Quality**: Continue professional development practices

## Testing the Implementation

To test this implementation:
1. Create a new chat/project
2. Send a first message requesting an app
3. Verify the agent follows the 5-tool-call sequence
4. Check that files respect size limits
5. Confirm the app works immediately
6. Test that subsequent messages follow established patterns

## Rollback Plan

If issues arise, the implementation can be quickly reverted by:
1. Changing `agentModeEnabled = true` back to `false` for first messages
2. Reverting the prompt selection to use `COMPLETE_STREAMLINED_PROMPT`
3. Removing `generateDesign` from first message enabled tools

The changes are isolated and can be toggled with minimal risk.
