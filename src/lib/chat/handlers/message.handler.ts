import {
    type Message,
    convertToCoreMessages,
    CoreMessage,
    CoreUserMessage,
    ImagePart,
    TextPart, UserContent, CoreSystemMessage
} from 'ai';
import {onboardingPrompt, systemPrompt} from "@/lib/ai/prompts";
import {FileLineManager} from "@/lib/editor/FileLineManager";
import {ComponentContext} from "@/types/component-context";
import {COMPLETE_STREAMLINED_PROMPT} from '@/lib/ai/prompt-directory';
import {Extractor} from "@/lib/chat/handlers/file-message/extractor";
import {CONCISE_AGENT_PROMPT} from "@/lib/ai/prompts/concise-agent-prompt";
import {STREAMLINED_AGENT_PROMPT} from "@/lib/ai/prompts/streamlined-agent-prompt";
import {AGENT_PROMPT, COMPLETE_AGENT_PROMPT} from "@/lib/ai/prompts/agent-prompt";
import {
    DISCUSS_MODE_PROMPT,
    DISCUSS_MODE_ERROR_FIX_PROMPT,
    DISCUSS_MODE_CODE_REVIEW_PROMPT
} from "@/lib/ai/prompts/discuss-mode-prompt";
import {convertToUIMessages} from "@/lib/utils";
import {STREAMLINED_V1_IMPLEMENTATION_PROMPT} from "@/lib/ai/prompt-directory/v1-implementation-prompt";
import {DESIGN_FIRST_AGENT_PROMPT} from "@/lib/ai/prompts/design-first-agent-prompt";
import posthogClient from "@/lib/posthog/posthog.client";

/**
 * MessageHandler
 *
 * Responsible for all message-related operations including:
 * - Message filtering and preparation
 * - Message transformation
 * - Message validation
 */
export class MessageHandler {
    private messagesWithTurns: Array<Message> = [];
    private coreMessages: CoreMessage[] = [];
    private userMessage: CoreMessage | null = null;
    private filteredMessages: CoreMessage[] = [];
    private systemPrompts: CoreSystemMessage[] = []
    private fileMessage: CoreMessage | null = null;
    private componentContexts: {componentName: string;
    element: string;
    sourceFile: string;
    lineNumber: number;
    imageUrl?: string;}[] = []
    private extractor: Extractor = new Extractor();

    /**
     * Initialize the handler with messages
     * This sets up the internal state for processing
     */
    public async initialize(messages: Array<Message>, minMessages: number = 5, options: {
        projectId?: string,
        backendEnabled?: boolean,
        componentContexts?: ComponentContext[],
        isFirstUserMessage?: boolean,
        agentModeEnabled?: boolean,
        userId: string,
        isDiscussion?: boolean,
        discussionType?: 'error-fix' | 'code-review' | 'general-discussion',
        hasScreenshots?: boolean  // NEW: Screenshot detection flag
    }): Promise<void> {
        let system;
        const matchedFlagPayload = await posthogClient.getFeatureFlagPayload('design-preview-feature', options.userId)

        // Handle discussion mode first (highest priority)
        if (options?.isDiscussion) {
            switch (options.discussionType) {
                case 'error-fix':
                    system = DISCUSS_MODE_ERROR_FIX_PROMPT;
                    break;
                case 'code-review':
                    system = DISCUSS_MODE_CODE_REVIEW_PROMPT;
                    break;
                default:
                    system = DISCUSS_MODE_PROMPT;
                    break;
            }
        } else if (options?.isFirstUserMessage) {
            // CHANGED: Use new design-first agent prompt for first messages
            system = DESIGN_FIRST_AGENT_PROMPT;
        } else {
            if (options?.agentModeEnabled) {
                system = STREAMLINED_AGENT_PROMPT
            } else {
                system = systemPrompt;
            }
        }

            // .replace(/{{BACKEND_PROMPT}}/g, options.backendEnabled ? backendPrompt : "There is no backend configured. NO MATTER WHAT, don't write backend code even if the user asks for it.");

        if(options.backendEnabled) {
            system = system.replace(/{{BACKEND_URL}}/g, `https://${process.env.VERCEL_URL || process.env.BACKEND_BASE_URL}/api/dynamic-backend/${options.projectId}/latest`);
        }

        // console.log('Replacing backend prompt, enabled:', options.backendEnabled)

        this.systemPrompts = [
            {
                role: 'system',
                content: system
            },
    //         {
    //             role: 'system',
    //             content: ` <extended_system_prompt>
    //     <title>From the system to you:</title>
    //     <extended_system_prompt_reminder>Remember, you are building a mobile app and the user needs to get a wow factor. Use spacing as a design element and keep it minimal and clean.</extended_system_prompt_reminder>
    //     <reminders>
    //         <reminder id="1">Read the system prompt very carefully.</reminder>
    //         <reminder id="2">[ULTRA IMPORTANT]: Always follow the MO_FILE response formats. Use MO_DIFF whenever you can to save costs. </reminder>
    //         <reminder id="3">[ULTRA IMPORTANT]: NEVER change the existing design/layout/functionality of a screen when making edits unless explicitly specified.</reminder>
    //         <reminder id="4">[ULTRA IMPORTANT]: Return complete implementations of every file, do not leave it for the user to implement. The user does not have access to the code.</reminder>
    //         <reminder id="5">ALWAYS adhere to the "NEVER ASSUME THAT THE USER WILL MAKE CHANGES TO A FILE" section no matter what</reminder>
    //         <reminder id="6">ALWAYS provide complete implementations and never partial updates to files</reminder>
    //
    // </reminders>
    // </extended_system_prompt>`
    //         }
        ]
        this.messagesWithTurns = this.getMessagesWithCompleteTurns(messages, minMessages);
        this.coreMessages = convertToCoreMessages(this.messagesWithTurns);
        this.userMessage = this.getMostRecentUserMessage(this.coreMessages);
        this.filteredMessages = this.filterCoreMessages(this.coreMessages);
        if(options.componentContexts) {
            this.componentContexts = options.componentContexts;
        }
        this.appendMessageCountSystemMessage();
    }


    appendToSystemPrompt(content: string) {
        this.systemPrompts.push({
            role: 'system',
            content: content
        })
    }

    private appendMessageCountSystemMessage() {
        // Count how many user messages we've had
        const userMessageCount = this.messagesWithTurns.filter(m => m.role === 'assistant').length;

        // if (userMessageCount <= 3) {
        //     this.systemPrompts.push({
        //         role: "system",
        //         content: `${onboardingPrompt}
        //     <CurrentPhase>Message: ${userMessageCount}</CurrentPhase>`
        //     })
        // }
    }

    /**
     * Replace MO_FILE and MO_DIFF tags with a summary message
     * @param text The text to process
     * @returns The text with tags replaced
     */
    public replaceMoFileTags(text: string) {
        // Create proper RegExp objects with correct character classes to match any character including newlines
        const mofileregex = new RegExp(`<MO_FILE\\s+path="([^"]+)"[^>]*?>([\\s\\S]*?)</MO_FILE>`, 'g');
        const modiffregex = new RegExp(`<MO_DIFF\\s+path="([^"]+)"[^>]*?>([\\s\\S]*?)</MO_DIFF>`, 'g');

        // For MO_FILE tags, extract the path and replace with a summary
        const fileReplacementContent = (match: string, path: string, content: string) => {
            // Create a summary of what was changed
            return `[Created/Modified file: ${path}]`;
        };

        // For MO_DIFF tags, extract the path and replace with a summary
        const diffReplacementContent = (match: string, path: string, content: string) => {
            // Create a summary of what was changed
            return `[Modified file: ${path}]`;
        };

        // Replace both MO_FILE and MO_DIFF tags with summaries
        let replaced = text.replace(mofileregex, fileReplacementContent);
        return replaced.replace(modiffregex, diffReplacementContent);
    }

    private removeCode(message: CoreMessage) {
        if (typeof message.content === "string") {
            message.content = this.replaceMoFileTags(message.content);
        } else {
            message.content = message.content.map(cont => {
                if (cont.type === "text") {
                    cont.text = this.replaceMoFileTags(cont.text);
                }
                return cont;
            }) as any
        }
        return message;
    }
    
    /**
     * Truncate tool message content to save context window space
     * @param message The tool message to truncate
     * @param maxLength Maximum allowed content length (default: 3000)
     * @returns The message with truncated content
     */
    private truncateToolContent(message: CoreMessage, maxLength: number = 3000): CoreMessage {
        if (!message) return message;
        
        if (typeof message.content === "string") {
            if (message.content.length > maxLength) {
                message.content = message.content.slice(0, maxLength) + 
                    '\n// Tool response truncated to save context window size. Focus on the available information.';
            }
        } else if (Array.isArray(message.content)) {
            message.content = message.content.map(cont => {
                // Handle text type content
                if (cont.type === "text" && cont.text.length > maxLength) {
                    return {
                        ...cont,
                        text: cont.text.slice(0, maxLength) + 
                            '\n// Tool response truncated to save context window size. Focus on the available information.'
                    };
                }
                
                // Handle tool-result type content
                if (cont.type === "tool-result" && cont.result && typeof cont.result === 'string' && cont.result.length > maxLength) {
                    return {
                        ...cont,
                        result: cont.result.slice(0, maxLength) + 
                            '\n// Tool response truncated to save context window size. Focus on the available information.'
                    };
                }
                
                return cont;
            }) as any;
        }
        
        return message;
    }


    public getMessagesForRequest({agentModeEnabled, applyCaching}: { agentModeEnabled: boolean, applyCaching?: boolean }) {
        let messages: CoreMessage[] = [
            ...this.systemPrompts
        ]

        if (this.fileMessage) {
            messages.push(this.fileMessage)
        }

        const latestUserMessageIndex = this.getMostRecentUserMessageIndex(this.filteredMessages);
        const clonedFilteredMessages = [...this.filteredMessages.toSpliced(latestUserMessageIndex, 1)];
        if (latestUserMessageIndex !== -1 && this.userMessage) {
            clonedFilteredMessages[latestUserMessageIndex] = this.userMessage;
        }

        messages = messages.concat(clonedFilteredMessages)
        if(applyCaching) {
            messages = this.applyCaching(messages);
        }
        messages = messages.filter(m => !!m);

        if (agentModeEnabled) {
            // In agent mode, remove MO tags from all messages
            messages = messages.map(message => {
                if(message.role === "assistant") {
                    message = this.removeCode(message);
                }
                if(message.role === "tool") {
                    message = this.truncateToolContent(message);
                }
                return message;
            });
        } else {
            // Find all assistant messages with MO_FILE tags
            const assistantMessagesWithMOFILE = messages.map((message, index) => {
                const content: string = (message.content?.[0] as TextPart)?.text || message.content as string;
                return {
                    index,
                    hasMOFILE: message.role === "assistant" && content.includes("MO_FILE")
                };
            }).filter(item => item.hasMOFILE);

            // Keep the last 2 messages with MO_FILE tags intact
            const keepLastN = 2;
            const messagesToKeep = assistantMessagesWithMOFILE.slice(-keepLastN).map(item => item.index);

            messages = messages.map((message, index) => {
                return messagesToKeep.includes(index) ? message : this.removeCode(message);
            });
        }

        return messages
    }

    private applyCaching(messages: CoreMessage[]) {
        const lastSystemMessageIndex = messages.findIndex(m => m.role === 'system');
        if (lastSystemMessageIndex !== -1) {
            console.log('Adding cache to last system message')
            messages[lastSystemMessageIndex] = this.appendCacheTag(messages[lastSystemMessageIndex]);
        }

        // const fileMessageIndex = messages.findIndex((message: any) => {
        //     if (message.role === "system") {
        //         return Array.isArray(message.content) ?
        //             message.content.some(
        //                 (text: any) => {
        //                     return text.text?.includes("<FILE_MESSAGE>")
        //                 }
        //             ) : message.content.includes("<FILE_MESSAGE>")
        //     }
        //     return false;
        // });
        // if (fileMessageIndex !== -1) {
        //     console.log('Adding cache to file message')
        //     messages[fileMessageIndex] = this.appendCacheTag(messages[fileMessageIndex]);
        // }

        // Find first user message
        const firstAssistantResponseIndex = messages.findIndex((message: any) => message?.role === "assistant");
        if (firstAssistantResponseIndex !== -1) {
            console.log('Adding cache first assistant response')
            messages[firstAssistantResponseIndex] = this.appendCacheTag(messages[firstAssistantResponseIndex]);
        }

        const lastAssistantResponseIndex = messages.findLastIndex((message: any) => message?.role === "assistant");
        if (lastAssistantResponseIndex !== -1) {
            console.log('Adding cache to last assistant response')
            messages[lastAssistantResponseIndex] = this.appendCacheTag(messages[lastAssistantResponseIndex]);
        }
        return messages;
    }

    private appendCacheTag(message: CoreMessage) {
        if (typeof message.content === "string") {
            message =  {
                ...message,
                providerOptions: {
                    openrouter: {
                        // cache_control also works
                        // cache_control: { type: 'ephemeral' }
                        cacheControl: { type: 'ephemeral' },
                    },
                },
                experimental_providerMetadata: {
                    openrouter: {
                        // cache_control also works
                        // cache_control: { type: 'ephemeral' }
                        cacheControl: { type: 'ephemeral' },
                    },
                },
                // @ts-ignore
                providerMetadata: {
                    openrouter: {
                        // cache_control also works
                        // cache_control: { type: 'ephemeral' }
                        cacheControl: { type: 'ephemeral' },
                    },
                },
            }
        } else {
            message.content[message.content.length - 1] = {
                ...message.content[message.content.length - 1],
                // @ts-ignore
                providerOptions: {
                    openrouter: {
                        // cache_control also works
                        // cache_control: { type: 'ephemeral' }
                        cacheControl: { type: 'ephemeral' },
                    },
                },
                experimental_providerMetadata: {
                    openrouter: {
                        // cache_control also works
                        // cache_control: { type: 'ephemeral' }
                        cacheControl: { type: 'ephemeral' },
                    },
                },
                // @ts-ignore
                providerMetadata: {
                    openrouter: {
                        // cache_control also works
                        // cache_control: { type: 'ephemeral' }
                        cacheControl: { type: 'ephemeral' },
                    },
                },
            }
        }

        return message;
    }

    /**
     * Get the current user message
     */
    public getCurrentUserMessage(): CoreMessage | null {
        return this.userMessage;
    }

    public getCurrentUserMessageForUI() {
        const messages =  convertToUIMessages([this.userMessage || {} as any])
        return messages[0];
    }

    /**
     * Set the current user message
     * Useful when the message has been enhanced with additional context
     */
    public setCurrentUserMessage(message: CoreMessage): void {
        this.userMessage = message;
    }

    /**
     * Get messages with complete conversation turns
     * Ensures we have complete context by including the most recent messages
     * plus any older messages that form complete conversation turns
     */
    public getMessagesWithCompleteTurns(messages: Array<Message>, minMessages: number = 10): Array<Message> {
        if (messages.length <= minMessages) {
            return messages;
        }

        const recentMessages = messages.slice(-minMessages);
        const remainingMessages = messages.slice(0, -minMessages);
        const oldestUserMessageIndex = remainingMessages.map(m => m.role).lastIndexOf('user');

        if (oldestUserMessageIndex === -1) {
            return recentMessages;
        }

        const additionalMessages = remainingMessages.slice(oldestUserMessageIndex);
        return [...additionalMessages, ...recentMessages];
    }

    /**
     * Get the most recent user message from a list of messages
     */
    public getMostRecentUserMessage(messages: CoreMessage[]): CoreMessage | null {
        for (let i = messages.length - 1; i >= 0; i--) {
            if (messages[i].role === 'user') {
                return messages[i];
            }
        }
        return null;
    }

    public getMostRecentUserMessageIndex(messages: CoreMessage[]): number {
        for (let i = messages.length - 1; i >= 0; i--) {
            if (messages[i].role === 'user') {
                return i;
            }
        }
        return -1;
    }

    /**
     * Filter core messages to optimize context window usage
     * - Keeps all user messages
     * - Keeps assistant messages without tool calls
     * - For assistant messages with tool calls, keeps the last 4 and removes tool-call content
     * - Removes all tool messages
     */
    public filterCoreMessages(messages: CoreMessage[]): CoreMessage[] {
        // Find last 4 assistant messages that have tool-call content (increased from 2)
        const findSecondLastUserMessageIndexArray: number[] = messages
            .map((message, index) => message.role === "user" ? index : undefined)
            .filter(m => typeof m !== "undefined");
        const secondLastUserMessageIndex = findSecondLastUserMessageIndexArray.slice(-3)[0];
        const assistantMessagesWithTools = messages
            .filter((msg, index) => msg.role === 'assistant' &&
                index > secondLastUserMessageIndex)
             // Increased from 2 to 4 to provide more context

        const toolCallIDs = assistantMessagesWithTools.reduce((acc, message) => {
            if (Array.isArray(message.content)) {
                const ids = message.content.map(cont => {
                    return cont.type === "tool-call" ? cont.toolCallId : null;
                }).filter(id => !!id);
                acc = acc.concat(ids);
            }
            return acc;
        }, [] as string[])


        return messages.filter(msg => {
            // Keep user messages
            if (msg.role === 'user') return true;

            // For assistant messages
            if (msg.role === 'assistant') {
                // If it has tool calls, only keep last 2 and remove tool-call content
                if (Array.isArray(msg.content) && msg.content.some(c => c.type === 'tool-call')) {
                    if (assistantMessagesWithTools.includes(msg)) {
                        // Keep only text content
                        // msg.content = msg.content.filter(c => c.type === 'text');
                        return true;
                    }
                    return false;
                }
                return true; // Keep assistant messages without tool calls
            }

            // Remove tool messages not in the whitelisted ids
            if (msg.role === 'tool') {
                const allowed = Array.isArray(msg.content) && msg.content.some(cont => toolCallIDs.includes(cont.toolCallId));
                return allowed;
            }

            return false;
        });
    }

    /**
     * Prepare user message for LLM processing
     * - Handles both array and string content formats
     * - Ensures image parts have correct type information
     */
    public prepareUserMessage(userMessage: CoreMessage): CoreMessage {
        if (!Array.isArray(userMessage.content)) {
            return userMessage;
        }

        // Process array content
        const processedContent = userMessage.content.map(content => {
            if (content.type === "image") {
                return {
                    type: 'image',
                    mimeType: "image/png",
                    image: content.image
                } as ImagePart;
            }
            return content;
        }) as UserContent;

        return {
            ...userMessage,
            content: processedContent
        } as CoreMessage;
    }

    /**
     * Extracts import statements from message content
     * Useful for analyzing code snippets and understanding dependencies
     * @param content Array of messages to analyze
     * @returns Array of extracted import statements
     */
    public extractImportsFromContent(content: string): string[] {
        const importStatements: string[] = [];
        const importRegex = /import\s+(?:{[^}]*}|\*\s+as\s+[^;]+|[^;{]*)\s+from\s+['"][^'"]+['"];?|import\s+['"][^'"]+['"];?/g;

        const matches = content.match(importRegex)
        if (matches) {
            importStatements.push(...matches);
        }

        // Remove duplicates and return
        return [...new Set(importStatements)];
    }

    /**
     * Append additional context to user message
     * This can be used to add system instructions or other context
     */
    public appendToUserMessage(userMessage: CoreMessage, additionalContext: string): CoreMessage {
        if (Array.isArray(userMessage.content)) {
            return {
                ...userMessage,
                content: userMessage.content.map(content => {
                    if (content.type === "text") {
                        return {
                            ...content,
                            text: content.text + additionalContext
                        } as TextPart;
                    }
                    return content;
                }) as UserContent
            } as CoreMessage;
        } else {
            return {
                ...userMessage,
                content: userMessage.content + additionalContext
            } as CoreMessage;
        }
    }

    /**
     * Create a file message from the provided files
     * This formats the files in a way that can be included in the message context
     */
    public createFileMessage(files: any[], fileManager: FileLineManager, useCache: boolean = false, agentModeEnabled = false, isFirstMessage= false) {
        const textContent = `<FILE_MESSAGE>
You have access to only these files and you cannot create any assets like mp3 or fonts or lottie. Remember, the project is running in expo snack.
Files like app.json, package.json, babel.config, metro.config or any other configuration files are automatically taken care of.

Given the following files in a Expo React Native project:

${files.map((file) => {
    const fileCount = this.extractor.getFileLineCount(file.content);
    const warnings: string[] = [];

    if(fileCount.warning) {
        warnings.push(fileCount.warning);
    }
                        return `

---- File: ------------
Path: ${file.name}
FileType: ${this.extractor.getFileType(file.name)}
Number of lines: ${fileCount.count}
Warnings to solve: ${warnings.join(',')}
File Contents
---------
    ${
       agentModeEnabled && !isFirstMessage ?
       this.extractor.extractMinimalFileStructure(file.content) :
       file.content
    }
    `;
                    }).join('')}
${agentModeEnabled ? this.extractor.generateMinimalDependencyGraph(files) : ''}
Answer the user's question only to be able write code and nothing else.
                    `;

        const messageContent: TextPart = {
            type: "text",
            text: textContent
        };

        // // Only add cache_control if this is a base version
        // if (useCache) {
        //     messageContent.cache_control = { type: "ephemeral" };
        // }

        this.fileMessage = {
            role: "user",
            content: textContent
        };
    }

    /**
     * Enhance user message with additional context
     * - Adds Supabase prompt if available
     * - Can be extended to add other context as needed
     */
    public enhanceUserMessage(supabasePrompt?: string) {
        if (!supabasePrompt || !this.userMessage) {
            return;
        }
        this.userMessage = this.appendToUserMessage(this.userMessage, `\n${supabasePrompt}`);

    }

    /**
     * Create a message object ready for saving to the database
     * - Formats the message with all required fields
     * - Handles proper processing of content
     */
    public createMessageForSaving(
        message: CoreMessage,
        messageId: string,
        chatId: string,
        userId: string,
        autoFixed: boolean,
    ): any {
        return {
            ...this.prepareUserMessage(message),
            id: messageId,
            createdAt: new Date(),
            chatId: chatId,
            userId: userId,
            componentContexts: this.componentContexts,
            autoFixed,
            hidden: autoFixed
        };
    }
}
